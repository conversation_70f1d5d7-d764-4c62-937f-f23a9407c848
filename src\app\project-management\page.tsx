'use client'

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  Building2,
  Calendar,
  Users,
  FileText,
  BarChart3,
  Settings,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  Home,
  MapPin,
  CreditCard,
  Crown
} from 'lucide-react';

export default function ProjectManagementPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('Dashboard');

  const sidebarTabs = [
    { id: 'Dashboard', label: 'Dashboard', icon: Home },
    { id: 'Site', label: 'Site', icon: MapPin },
    { id: 'Transactions', label: 'Transactions', icon: CreditCard },
    { id: 'Subscription', label: 'Subscription', icon: Crown },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="pt-20 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex gap-6">
            {/* Sidebar */}
            <div className="w-64 flex-shrink-0">
              <Card className="sticky top-24">
                <CardContent className="p-0">
                  {/* Infratask Shop Header */}
                  <div className="border-b border-gray-200 p-4">
                    <button
                      onClick={() => router.push('/')}
                      className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      <ArrowLeft className="h-4 w-4 mr-3" />
                      Infratask Shop
                    </button>
                  </div>

                  {/* Tab Navigation */}
                  <div className="space-y-1 p-4">
                    {sidebarTabs.map((tab) => {
                      const IconComponent = tab.icon;
                      return (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                            activeTab === tab.id
                              ? 'bg-[#f97316] text-white'
                              : 'text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          <IconComponent className="h-4 w-4 mr-3" />
                          {tab.label}
                        </button>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <div className="flex-1 min-w-0">
              {/* Header */}
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center">
                  <h1 className="text-3xl font-bold text-gray-900">{activeTab}</h1>
                </div>
                {activeTab === 'Dashboard' && (
                  <Button className="bg-[#f97316] hover:bg-[#ea580c] text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    New Project
                  </Button>
                )}
              </div>

              {/* Tab Content */}
              {activeTab === 'Dashboard' && (
                <>
                  {/* Stats Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <Building2 className="h-6 w-6 text-blue-600" />
                          </div>
                          <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Total Projects</p>
                            <p className="text-2xl font-bold text-gray-900">12</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center">
                          <div className="p-2 bg-green-100 rounded-lg">
                            <CheckCircle className="h-6 w-6 text-green-600" />
                          </div>
                          <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Completed</p>
                            <p className="text-2xl font-bold text-gray-900">8</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center">
                          <div className="p-2 bg-yellow-100 rounded-lg">
                            <Clock className="h-6 w-6 text-yellow-600" />
                          </div>
                          <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">In Progress</p>
                            <p className="text-2xl font-bold text-gray-900">3</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center">
                          <div className="p-2 bg-red-100 rounded-lg">
                            <AlertCircle className="h-6 w-6 text-red-600" />
                          </div>
                          <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Overdue</p>
                            <p className="text-2xl font-bold text-gray-900">1</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Main Content */}
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Recent Projects */}
                    <div className="lg:col-span-2">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center">
                            <Building2 className="h-5 w-5 mr-2" />
                            Recent Projects
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {[
                              { name: "Residential Complex A", status: "In Progress", progress: 75, dueDate: "Dec 15, 2024" },
                              { name: "Commercial Building B", status: "Completed", progress: 100, dueDate: "Nov 30, 2024" },
                              { name: "Infrastructure Project C", status: "Planning", progress: 25, dueDate: "Jan 20, 2025" },
                            ].map((project, index) => (
                              <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                <div className="flex items-center justify-between mb-2">
                                  <h3 className="font-semibold text-gray-900">{project.name}</h3>
                                  <Badge
                                    variant={project.status === 'Completed' ? 'default' : 'secondary'}
                                    className={
                                      project.status === 'Completed' ? 'bg-green-100 text-green-800' :
                                      project.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                                      'bg-yellow-100 text-yellow-800'
                                    }
                                  >
                                    {project.status}
                                  </Badge>
                                </div>
                                <div className="flex items-center justify-between text-sm text-gray-600">
                                  <span>Progress: {project.progress}%</span>
                                  <span>Due: {project.dueDate}</span>
                                </div>
                                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                                  <div
                                    className="bg-[#f97316] h-2 rounded-full"
                                    style={{ width: `${project.progress}%` }}
                                  ></div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Quick Actions */}
                    <div className="lg:col-span-1">
                      <Card className="mb-6">
                        <CardHeader>
                          <CardTitle>Quick Actions</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <Button variant="outline" className="w-full justify-start">
                            <Calendar className="h-4 w-4 mr-2" />
                            Schedule Meeting
                          </Button>
                          <Button variant="outline" className="w-full justify-start">
                            <FileText className="h-4 w-4 mr-2" />
                            Generate Report
                          </Button>
                          <Button variant="outline" className="w-full justify-start">
                            <Users className="h-4 w-4 mr-2" />
                            Manage Team
                          </Button>
                          <Button variant="outline" className="w-full justify-start">
                            <BarChart3 className="h-4 w-4 mr-2" />
                            View Analytics
                          </Button>
                          <Button variant="outline" className="w-full justify-start">
                            <Settings className="h-4 w-4 mr-2" />
                            Settings
                          </Button>
                        </CardContent>
                      </Card>

                      {/* Upcoming Deadlines */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center">
                            <Clock className="h-5 w-5 mr-2" />
                            Upcoming Deadlines
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-sm">Material Delivery</p>
                                <p className="text-xs text-gray-600">Project A</p>
                              </div>
                              <Badge variant="secondary" className="bg-red-100 text-red-800">
                                2 days
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-sm">Site Inspection</p>
                                <p className="text-xs text-gray-600">Project B</p>
                              </div>
                              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                                5 days
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-sm">Final Review</p>
                                <p className="text-xs text-gray-600">Project C</p>
                              </div>
                              <Badge variant="secondary" className="bg-green-100 text-green-800">
                                1 week
                              </Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </>
              )}

              {/* Site Tab Content */}
              {activeTab === 'Site' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <MapPin className="h-5 w-5 mr-2" />
                        Site Management
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {[
                          { name: "Site A - Downtown", status: "Active", workers: 25, progress: 80 },
                          { name: "Site B - Suburbs", status: "Planning", workers: 0, progress: 15 },
                          { name: "Site C - Industrial", status: "Active", workers: 40, progress: 60 },
                        ].map((site, index) => (
                          <Card key={index} className="border">
                            <CardContent className="p-4">
                              <h3 className="font-semibold text-gray-900 mb-2">{site.name}</h3>
                              <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                  <span>Status:</span>
                                  <Badge variant={site.status === 'Active' ? 'default' : 'secondary'}>
                                    {site.status}
                                  </Badge>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span>Workers:</span>
                                  <span>{site.workers}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span>Progress:</span>
                                  <span>{site.progress}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div
                                    className="bg-[#f97316] h-2 rounded-full"
                                    style={{ width: `${site.progress}%` }}
                                  ></div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Transactions Tab Content */}
              {activeTab === 'Transactions' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <CreditCard className="h-5 w-5 mr-2" />
                        Recent Transactions
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {[
                          { id: "TXN-001", description: "Material Purchase - Cement", amount: "$2,500", date: "Dec 10, 2024", status: "Completed" },
                          { id: "TXN-002", description: "Equipment Rental", amount: "$1,200", date: "Dec 8, 2024", status: "Pending" },
                          { id: "TXN-003", description: "Labor Payment", amount: "$3,800", date: "Dec 5, 2024", status: "Completed" },
                        ].map((transaction, index) => (
                          <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div className="flex items-center justify-between">
                              <div>
                                <h3 className="font-semibold text-gray-900">{transaction.description}</h3>
                                <p className="text-sm text-gray-600">{transaction.id} • {transaction.date}</p>
                              </div>
                              <div className="text-right">
                                <p className="font-semibold text-gray-900">{transaction.amount}</p>
                                <Badge variant={transaction.status === 'Completed' ? 'default' : 'secondary'}>
                                  {transaction.status}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Subscription Tab Content */}
              {activeTab === 'Subscription' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Crown className="h-5 w-5 mr-2" />
                        Subscription Plans
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {[
                          { name: "Basic", price: "$29", period: "/month", features: ["Up to 5 projects", "Basic reporting", "Email support"] },
                          { name: "Professional", price: "$79", period: "/month", features: ["Up to 20 projects", "Advanced analytics", "Priority support", "Team collaboration"], current: true },
                          { name: "Enterprise", price: "$199", period: "/month", features: ["Unlimited projects", "Custom integrations", "Dedicated support", "Advanced security"] },
                        ].map((plan, index) => (
                          <Card key={index} className={`border ${plan.current ? 'border-[#f97316] bg-orange-50' : ''}`}>
                            <CardContent className="p-6">
                              <div className="text-center">
                                <h3 className="font-semibold text-lg">{plan.name}</h3>
                                {plan.current && (
                                  <Badge className="mb-2 bg-[#f97316]">Current Plan</Badge>
                                )}
                                <div className="text-3xl font-bold text-gray-900 mb-4">
                                  {plan.price}<span className="text-sm font-normal text-gray-600">{plan.period}</span>
                                </div>
                                <ul className="space-y-2 text-sm text-gray-600 mb-6">
                                  {plan.features.map((feature, featureIndex) => (
                                    <li key={featureIndex} className="flex items-center">
                                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                                      {feature}
                                    </li>
                                  ))}
                                </ul>
                                <Button
                                  className={`w-full ${plan.current ? 'bg-gray-400' : 'bg-[#f97316] hover:bg-[#ea580c]'}`}
                                  disabled={plan.current}
                                >
                                  {plan.current ? 'Current Plan' : 'Upgrade'}
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
